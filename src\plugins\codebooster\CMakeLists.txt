cmake_minimum_required(VERSION 3.16)

project(CodeBooster)

# set(CMAKE_AUTOMOC ON)
# set(CMAKE_AUTORCC ON)
# set(CMAKE_AUTOUIC ON)
# set(CMAKE_CXX_STANDARD 17)
# set(QtCreator_DIR "C:/Qt/QtCreator/qtcreator-windows-x64-msvc-11.0.0/lib/cmake/QtCreator")

# find_package(QtCreator REQUIRED COMPONENTS Core)
# find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets)
# find_package(Qt6 REQUIRED COMPONENTS LinguistTools)
# set(QtX Qt${QT_VERSION_MAJOR})

# # Add a CMake option that enables building your plugin with tests.
# # You don't want your released plugin binaries to contain tests,
# # so make that default to 'NO'.
# # Enable tests by passing -DWITH_TESTS=ON to CMake.
# option(WITH_TESTS "Builds with tests" NO)

# if(WITH_TESTS)
#   # Look for QtTest
#   find_package(${QtX} REQUIRED COMPONENTS Test)

#   # Tell CMake functions like add_qtc_plugin about the QtTest component.
#   set(IMPLICIT_DEPENDS Qt::Test)

#   # Enable ctest for auto tests.
#   enable_testing()
# endif()


add_qtc_plugin(CodeBooster
  PLUGIN_DEPENDS
    QtCreator::Core
    QtCreator::LanguageClient
  DEPENDS
    Qt::Widgets
    Qt::Network
    Qt::Sql
    QtCreator::ExtensionSystem
    QtCreator::Utils
    QtCreator::ProjectExplorer
    QtCreator::TextEditor
  SOURCES
    codebooster.qrc
    codeboosterclient.cpp
    codeboosterclient.h
    codeboosterconstants.h
    codeboosterhoverhandler.cpp
    codeboosterhoverhandler.h



    codeboosterplugin.cpp
    codeboosterplugin.h




    codeboostersuggestion.cpp
    codeboostersuggestion.h
    codeboostertr.h
    requests/getcompletions.h
    codeboosterclientinterface.h
    codeboosterclientinterface.cpp




)

target_sources(CodeBooster
  PRIVATE
    chatsidebar/chatviewfactory.cpp
    chatsidebar/chatviewfactory.h
    chatsidebar/chatview.cpp
    chatsidebar/chatview.h
    chatsidebar/chatview.ui





    chatsidebar/chatexportdialog.cpp
    chatsidebar/chatexportdialog.h
    chatsidebar/chatexportdialog.ui
    chatsidebar/md4c/entity.c
    chatsidebar/md4c/entity.h
    chatsidebar/md4c/md4c.c
    chatsidebar/md4c/md4c.h
    chatsidebar/md4c/md4c-html.c
    chatsidebar/md4c/md4c-html.h
    chatsidebar/qmarkdowntextedit/linenumberarea.h
    chatsidebar/qmarkdowntextedit/markdownhighlighter.cpp
    chatsidebar/qmarkdowntextedit/markdownhighlighter.h
    chatsidebar/qmarkdowntextedit/media.qrc
    chatsidebar/qmarkdowntextedit/qmarkdowntextedit.cpp
    chatsidebar/qmarkdowntextedit/qmarkdowntextedit.h
    chatsidebar/qmarkdowntextedit/qownlanguagedata.cpp
    chatsidebar/qmarkdowntextedit/qownlanguagedata.h
    chatsidebar/qmarkdowntextedit/qplaintexteditsearchwidget.cpp
    chatsidebar/qmarkdowntextedit/qplaintexteditsearchwidget.h
    chatsidebar/qmarkdowntextedit/qplaintexteditsearchwidget.ui
    chatsidebar/markdownpreview/codetohtmlconverter.cpp
    chatsidebar/markdownpreview/codetohtmlconverter.h
    chatsidebar/markdownpreview/filedialog.cpp
    chatsidebar/markdownpreview/filedialog.h
    chatsidebar/markdownpreview/markdownhtmlconverter.cpp
    chatsidebar/markdownpreview/markdownhtmlconverter.h
    chatsidebar/markdownpreview/markdownpreview.pri
    chatsidebar/markdownpreview/markdownpreviewsetting.cpp
    chatsidebar/markdownpreview/markdownpreviewsetting.h
    chatsidebar/markdownpreview/messagepreviewwidget.cpp
    chatsidebar/markdownpreview/messagepreviewwidget.h
    chatsidebar/markdownpreview/misc.cpp
    chatsidebar/markdownpreview/misc.h
    chatsidebar/markdownpreview/notepreviewwidget.cpp
    chatsidebar/markdownpreview/notepreviewwidget.h
    chatsidebar/markdownpreview/qtexteditsearchwidget.cpp
    chatsidebar/markdownpreview/qtexteditsearchwidget.h
    chatsidebar/markdownpreview/qtexteditsearchwidget.ui
    chatsidebar/markdownpreview/resource.qrc
    chatsidebar/markdownpreview/schema.cpp
    chatsidebar/markdownpreview/schema.h
    chatsidebar/markdownpreview/schemes.conf


    chatsidebar/chathistorypage.h
    chatsidebar/chathistorypage.cpp
    chatsidebar/chathistorypage.ui
    chatsidebar/chatsessionwgt.h
    chatsidebar/chatsessionwgt.cpp
    chatsidebar/chatsessionwgt.ui






    askcodeboostertaskhandler.h
    askcodeboostertaskhandler.cpp


    chatsidebar/historylineedit.h chatsidebar/historylineedit.cpp

    chatsidebar/asksuggestionwgt.h chatsidebar/asksuggestionwgt.cpp


    chatsidebar/contextitemwidget.h chatsidebar/contextitemwidget.cpp chatsidebar/contextitemwidget.ui

    chatsidebar/slashcommand/slashcommand.cpp chatsidebar/slashcommand/slashcommand.h
    chatsidebar/slashcommand/commandlistwgt.h chatsidebar/slashcommand/commandlistwgt.cpp

    chatsidebar/inputWidget/inputwidget.cpp chatsidebar/inputWidget/inputwidget.h
    chatsidebar/inputWidget/codesnippetwidget.cpp chatsidebar/inputWidget/codesnippetwidget.h
    chatsidebar/inputWidget/customtextedit.cpp chatsidebar/inputWidget/customtextedit.h
    utility/instrumentor.h
    utility/customlinewidget.h
    utility/sliderindicator.cpp utility/sliderindicator.h
    utility/widgetcover.cpp utility/widgetcover.h
    utility/gitignoreparser.cpp utility/gitignoreparser.h
    common/widgettheme.cpp common/widgettheme.h
    common/useroptions.cpp common/useroptions.h
    common/codeboosterutils.cpp common/codeboosterutils.h
    common/llmiconfactory.h
    common/codeboostericons.h
    requests/replyparser.cpp requests/replyparser.h
    requests/networkrequestmanager.cpp requests/networkrequestmanager.h
    requests/promptbuilder.cpp requests/promptbuilder.h
    pluginsettings/codeboostersettings.cpp pluginsettings/codeboostersettings.h
    pluginsettings/codeboosterprojectpanel.cpp pluginsettings/codeboosterprojectpanel.h
    pluginsettings/codeboosteroptionspage.cpp pluginsettings/codeboosteroptionspage.h
    inlineedit/editorchatwindow.cpp inlineedit/editorchatwindow.h inlineedit/editorchatwindow.ui
    database/chatdatabase.cpp database/chatdatabase.h
    chatcontext/contextitem.cpp chatcontext/contextitem.h
    database/chatsession.cpp database/chatsession.h
    chatsidebar/contextBuilder/contextbuilderwgt.cpp chatsidebar/contextBuilder/contextbuilderwgt.h chatsidebar/contextBuilder/ContextTree.cpp chatsidebar/contextBuilder/ContextTree.h





)

qt_add_translations(CodeBooster TS_FILES CodeBooster_zh_CN.ts)
install(FILES ${PROJECT_BINARY_DIR}/CodeBooster_zh_CN.qm DESTINATION share/qtcreator/translations)
